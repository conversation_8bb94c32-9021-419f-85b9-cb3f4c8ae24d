# 🚀 Kling AI Follow Toolkit

Một công cụ GUI được xây dựng bằng PyQt6 để tự động follow người dùng trên nền tảng Kling AI.

## ✨ Tính năng

- **Import Cookie**: D<PERSON> dàng import cookie từ file .txt
- **Follow đơn lẻ**: <PERSON><PERSON><PERSON><PERSON> một Follow ID để follow
- **Follow hàng loạt**: <PERSON>h<PERSON><PERSON> danh sách Follow IDs để follow nhiều người cùng lúc
- **Theo dõi tiến trình**: Progress bar và log chi tiết
- **Báo cáo kết quả**: Hiển thị số lượng follow thành công/thất bại
- **Giao diện thân thiện**: GUI đẹp mắt và dễ sử dụng

## 📋 Yêu cầu

- Python 3.7+
- PyQt6
- requests

## 🛠️ Cài đặt

1. **<PERSON><PERSON> hoặc tải về dự án**

2. **Cài đặt dependencies**:
```bash
pip install PyQt6 requests
```

Hoặc nếu có file requirements.txt:
```bash
pip install -r requirements.txt
```

## 🚀 Cách sử dụng

### 1. Chạy ứng dụng
```bash
python kling_follow_gui.py
```

### 2. Import Cookie
- Click nút "Chọn file cookie (.txt)"
- Chọn file .txt chứa cookie của bạn (ví dụ: cookies.txt)
- Cookie sẽ được load và hiển thị trạng thái ✓

### 3. Nhập Follow IDs

**Chế độ đơn lẻ:**
- Chọn "Nhập 1 ID"
- Nhập Follow ID vào ô text (ví dụ: 36709052)

**Chế độ hàng loạt:**
- Chọn "Nhập nhiều IDs"
- Nhập danh sách IDs, mỗi ID một dòng:
```
36709052
12345678
87654321
```

### 4. Bắt đầu Follow
- Click "🚀 Bắt đầu Follow"
- Theo dõi tiến trình qua progress bar và log
- Xem kết quả cuối cùng với số lượng thành công/thất bại

### 5. Kết quả Test
✅ **Đã test thành công với:**
- File: `cookies.txt` (15 dòng cookie)
- ID: `36709052` (kết quả: Already followed)
- API response: `result=1, status=500, error: Already followed`

## 📁 Cấu trúc dự án

```
MIT Kling Follow/
├── main.py                 # Script gốc (command line)
├── kling_follow_gui.py     # GUI application
├── README.md              # Hướng dẫn sử dụng
└── venv/                  # Virtual environment
```

## 🔧 Cấu hình

### Lấy Cookie
1. Đăng nhập vào https://app.klingai.com
2. Mở Developer Tools (F12)
3. Vào tab Network
4. Thực hiện một action bất kỳ trên trang
5. Tìm request và copy Cookie từ Headers
6. Lưu vào file .txt

### Format Follow ID
- Follow ID là số nguyên (ví dụ: 123456)
- Có thể tìm trong URL profile người dùng

## ⚠️ Lưu ý

- **Tốc độ**: Tool có delay 500ms giữa các request để tránh spam API
- **Cookie**: Cần cookie hợp lệ và chưa hết hạn
- **Rate Limit**: Kling AI có thể có giới hạn số lượng follow/thời gian
- **Sử dụng có trách nhiệm**: Không spam hoặc lạm dụng

## 🐛 Xử lý lỗi

**Cookie không hợp lệ:**
- Kiểm tra cookie có đúng format
- Đảm bảo cookie chưa hết hạn
- Thử đăng nhập lại và lấy cookie mới

**Follow thất bại:**
- Kiểm tra Follow ID có đúng
- Kiểm tra kết nối internet
- Có thể đã đạt giới hạn follow

**Lỗi GUI:**
- Đảm bảo đã cài PyQt6
- Kiểm tra Python version >= 3.7

## 📝 Changelog

### v1.0
- Tính năng cơ bản: import cookie, follow single/multiple IDs
- GUI với PyQt6
- Progress tracking và logging
- Báo cáo kết quả chi tiết

## 📄 License

Dự án này được phát triển cho mục đích học tập và nghiên cứu.

## 🤝 Đóng góp

Mọi đóng góp và phản hồi đều được chào đón!
