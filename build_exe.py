#!/usr/bin/env python3
"""
Script để build file .exe cho Kling AI Follow Toolkit
"""

import os
import subprocess
import sys
import shutil

def build_exe():
    """Build file .exe với PyInstaller"""
    print("🚀 Bắt đầu build file .exe...")
    print("=" * 60)
    
    # Kiểm tra file chính
    main_file = "kling_follow_gui.py"
    if not os.path.exists(main_file):
        print(f"❌ Không tìm thấy file {main_file}")
        return False
    
    # T<PERSON>o thư mục build nếu chưa có
    build_dir = "build_output"
    if os.path.exists(build_dir):
        print(f"🗑️ Xóa thư mục build cũ: {build_dir}")
        shutil.rmtree(build_dir)
    
    # Tạo file .spec tùy chỉnh
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{main_file}'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'requests',
        'json',
        'threading',
        'concurrent.futures',
        'time'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='KlingAI_Follow_Toolkit',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    # Ghi file .spec
    spec_file = "kling_follow.spec"
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ Đã tạo file spec: {spec_file}")
    
    # Build với PyInstaller
    print("🔨 Đang build file .exe...")
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        "--distpath", build_dir,
        spec_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ Build thành công!")
            exe_path = os.path.join(build_dir, "KlingAI_Follow_Toolkit.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 File .exe: {exe_path}")
                print(f"📏 Kích thước: {file_size:.1f} MB")
                print()
                print("🎉 HOÀN THÀNH!")
                print("=" * 60)
                print(f"✅ File .exe đã được tạo tại: {exe_path}")
                print("💡 Bạn có thể copy file này để chạy trên máy khác")
                print("💡 Không cần cài Python hay thư viện gì thêm")
                return True
            else:
                print("❌ Không tìm thấy file .exe sau khi build")
                return False
        else:
            print("❌ Build thất bại!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi build: {str(e)}")
        return False

if __name__ == "__main__":
    success = build_exe()
    if success:
        print("\n🚀 Bạn có thể chạy file .exe ngay bây giờ!")
    else:
        print("\n❌ Build thất bại. Vui lòng kiểm tra lỗi ở trên.")
