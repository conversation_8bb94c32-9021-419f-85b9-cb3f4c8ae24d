#!/usr/bin/env python3
"""
Script để tạo package release hoàn chỉnh
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_release_package():
    """Tạo package release với tất cả files cần thiết"""
    print("📦 Tạo package release...")
    print("=" * 60)
    
    # Tên package
    version = datetime.now().strftime("%Y%m%d_%H%M")
    package_name = f"KlingAI_Follow_Toolkit_v{version}"
    package_dir = f"release_{package_name}"
    
    # <PERSON><PERSON><PERSON> thư mục cũ nếu có
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    # Tạo thư mục mới
    os.makedirs(package_dir)
    
    # Copy files cần thiết
    files_to_copy = [
        ("build_output/KlingAI_Follow_Toolkit.exe", "KlingAI_Follow_Toolkit.exe"),
        ("build_output/README.txt", "README.txt"),
        ("build_output/cookies_sample.txt", "cookies_sample.txt"),
    ]
    
    print("📁 Copy files...")
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(package_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"✅ {src} -> {dst}")
        else:
            print(f"⚠️ Không tìm thấy: {src}")
    
    # Tạo file hướng dẫn nhanh
    quick_guide = f"""🚀 HƯỚNG DẪN NHANH - KLING AI FOLLOW TOOLKIT
================================================================

📋 CÁC BƯỚC THỰC HIỆN:

1. 🍪 CHUẨN BỊ COOKIES:
   - Đổi tên "cookies_sample.txt" thành "cookies.txt"
   - Thay thế nội dung bằng cookies thật của bạn
   - Mỗi dòng là 1 cookie hoàn chỉnh

2. 🚀 CHẠY ỨNG DỤNG:
   - Double-click "KlingAI_Follow_Toolkit.exe"
   - Đợi ứng dụng khởi động

3. ⚙️ CÀI ĐẶT:
   - Import file "cookies.txt"
   - Chọn số luồng: 5 (khuyến nghị)
   - Chọn chế độ follow

4. 🎯 FOLLOW:
   - Nhập ID người muốn follow
   - Tick "Duplicate ID với tất cả cookies" (nếu muốn)
   - Click "Bắt đầu Follow"

💡 TIPS NHANH:
• Bắt đầu với 3-5 luồng
• File cookies.txt phải cùng thư mục với .exe
• Có thể dừng bất cứ lúc nào bằng nút "Dừng"

📖 Chi tiết xem file README.txt

================================================================
Phiên bản: {version}
Ngày tạo: {datetime.now().strftime("%d/%m/%Y %H:%M")}
================================================================"""
    
    with open(os.path.join(package_dir, "QUICK_START.txt"), 'w', encoding='utf-8') as f:
        f.write(quick_guide)
    
    print("✅ Đã tạo QUICK_START.txt")
    
    # Tạo file zip
    zip_name = f"{package_name}.zip"
    print(f"🗜️ Tạo file zip: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arc_name)
                print(f"📁 Thêm vào zip: {arc_name}")
    
    # Thống kê
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    
    print()
    print("🎉 HOÀN THÀNH!")
    print("=" * 60)
    print(f"📦 Package: {package_dir}/")
    print(f"🗜️ File zip: {zip_name}")
    print(f"📏 Kích thước zip: {zip_size:.1f} MB")
    print()
    print("📋 NỘI DUNG PACKAGE:")
    print("✅ KlingAI_Follow_Toolkit.exe - Ứng dụng chính")
    print("✅ README.txt - Hướng dẫn chi tiết")
    print("✅ QUICK_START.txt - Hướng dẫn nhanh")
    print("✅ cookies_sample.txt - File mẫu cookies")
    print()
    print("🚀 Bạn có thể chia sẻ file zip này!")
    print("💡 Người nhận chỉ cần giải nén và chạy .exe")

if __name__ == "__main__":
    create_release_package()
