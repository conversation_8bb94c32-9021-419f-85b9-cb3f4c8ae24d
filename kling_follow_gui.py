import sys
import json
import requests
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QTextEdit, QLineEdit,
                             QFileDialog, QMessageBox, QProgressBar, QGroupBox,
                             QRadioButton, QButtonGroup, QCheckBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QIcon


class FollowWorker(QThread):
    """Worker thread để thực hiện follow operations"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(int, int)  # success_count, total_count

    def __init__(self, cookies, follow_ids):
        super().__init__()
        self.cookies = cookies if isinstance(cookies, list) else [cookies]
        self.follow_ids = follow_ids
        self.success_count = 0
        self.total_count = len(follow_ids)
        self.current_cookie_index = 0

    def get_next_cookie(self):
        """Lấy cookie tiếp theo trong danh sách"""
        if not self.cookies:
            return None

        cookie = self.cookies[self.current_cookie_index]
        self.current_cookie_index = (self.current_cookie_index + 1) % len(self.cookies)
        return cookie

    def run(self):
        """Thực hiện follow cho từng ID với cookie rotation"""
        url = "https://api-app-global.klingai.com/api/user_follow/follow?caver=2"

        self.status_updated.emit(f"🔄 Sử dụng {len(self.cookies)} cookie(s) để follow {len(self.follow_ids)} ID(s)")

        base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://app.klingai.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'time-zone': 'Asia/Saigon',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }

        for i, follow_id in enumerate(self.follow_ids):
            success = False
            attempts = 0
            max_attempts = min(3, len(self.cookies))  # Thử tối đa 3 cookies hoặc hết cookies

            while not success and attempts < max_attempts:
                attempts += 1
                try:
                    # Lấy cookie tiếp theo
                    current_cookie = self.get_next_cookie()
                    if not current_cookie:
                        self.status_updated.emit("❌ Không có cookie khả dụng")
                        break

                    # Tạo headers với cookie hiện tại
                    headers = base_headers.copy()
                    headers['Cookie'] = current_cookie

                    payload = json.dumps({
                        "dstId": int(follow_id),
                        "followScene": "web_user_home",
                        "followId": int(follow_id)
                    })

                    cookie_num = ((self.current_cookie_index - 1) % len(self.cookies)) + 1
                    attempt_text = f" (thử lần {attempts})" if attempts > 1 else ""
                    self.status_updated.emit(f"🔄 [Cookie {cookie_num}/{len(self.cookies)}] Đang follow ID: {follow_id}{attempt_text}")

                    response = requests.post(url, headers=headers, data=payload, timeout=10)

                    if response.status_code == 200:
                        response_data = response.json()
                        # API Kling sử dụng result=1 cho thành công
                        if response_data.get('result') == 1:
                            if response_data.get('status') == 200:
                                # Follow thành công
                                self.success_count += 1
                                user_name = response_data.get('data', {}).get('userName', 'Unknown')
                                self.status_updated.emit(f"✓ [Cookie {cookie_num}] Follow thành công ID: {follow_id} ({user_name})")
                                success = True
                            elif response_data.get('status') == 500 and 'Already followed' in str(response_data.get('error', {})):
                                # Đã follow trước đó - coi như thành công
                                self.success_count += 1
                                self.status_updated.emit(f"✓ [Cookie {cookie_num}] ID: {follow_id} đã được follow trước đó")
                                success = True
                            else:
                                error_msg = response_data.get('message', 'Unknown error')
                                self.status_updated.emit(f"✗ [Cookie {cookie_num}] Follow thất bại ID: {follow_id} - {error_msg}")
                                # Nếu là lỗi "Service busy", thử cookie khác
                                if "Service busy" in error_msg and attempts < max_attempts:
                                    self.status_updated.emit(f"🔄 Thử cookie khác...")
                                    continue
                                else:
                                    success = True  # Dừng thử
                        else:
                            self.status_updated.emit(f"✗ [Cookie {cookie_num}] Follow thất bại ID: {follow_id} - {response_data.get('message', 'Unknown error')}")
                            success = True  # Dừng thử
                    else:
                        self.status_updated.emit(f"✗ [Cookie {cookie_num}] Follow thất bại ID: {follow_id} - HTTP {response.status_code}")
                        # Thử cookie khác nếu có lỗi HTTP
                        if attempts < max_attempts:
                            self.status_updated.emit(f"🔄 Thử cookie khác...")
                            continue
                        else:
                            success = True  # Dừng thử

                except Exception as e:
                    self.status_updated.emit(f"✗ [Cookie {cookie_num if 'cookie_num' in locals() else 'Unknown'}] Lỗi khi follow ID: {follow_id} - {str(e)}")
                    # Thử cookie khác nếu có exception
                    if attempts < max_attempts:
                        self.status_updated.emit(f"🔄 Thử cookie khác...")
                        continue
                    else:
                        success = True  # Dừng thử
            
            # Cập nhật progress
            progress = int((i + 1) / self.total_count * 100)
            self.progress_updated.emit(progress)
            
            # Nghỉ một chút để tránh spam API
            self.msleep(500)
        
        self.finished.emit(self.success_count, self.total_count)


class KlingFollowGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.cookies = []  # Danh sách tất cả cookies
        self.follow_worker = None
        self.init_ui()
        
    def init_ui(self):
        """Khởi tạo giao diện người dùng"""
        self.setWindowTitle("Kling AI Follow Toolkit")
        self.setGeometry(100, 100, 600, 700)
        
        # Widget chính
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout chính
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("🚀 Kling AI Follow Toolkit")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # Cookie section
        cookie_group = QGroupBox("📁 Import Cookie")
        cookie_layout = QVBoxLayout(cookie_group)
        
        cookie_button_layout = QHBoxLayout()
        self.cookie_button = QPushButton("Chọn file cookie (.txt)")
        self.cookie_button.clicked.connect(self.import_cookie)
        cookie_button_layout.addWidget(self.cookie_button)
        
        self.cookie_status = QLabel("Chưa import cookie")
        self.cookie_status.setStyleSheet("color: red;")
        cookie_button_layout.addWidget(self.cookie_status)
        
        cookie_layout.addLayout(cookie_button_layout)
        main_layout.addWidget(cookie_group)
        
        # Follow ID section
        follow_group = QGroupBox("👥 Follow IDs")
        follow_layout = QVBoxLayout(follow_group)
        
        # Radio buttons cho chế độ nhập
        mode_layout = QHBoxLayout()
        self.single_mode = QRadioButton("Nhập 1 ID")
        self.multiple_mode = QRadioButton("Nhập nhiều IDs")
        self.single_mode.setChecked(True)
        
        self.mode_group = QButtonGroup()
        self.mode_group.addButton(self.single_mode)
        self.mode_group.addButton(self.multiple_mode)
        
        self.single_mode.toggled.connect(self.on_mode_changed)
        
        mode_layout.addWidget(self.single_mode)
        mode_layout.addWidget(self.multiple_mode)
        follow_layout.addLayout(mode_layout)
        
        # Input cho single ID
        self.single_input = QLineEdit()
        self.single_input.setPlaceholderText("Nhập Follow ID (ví dụ: 36709052) - Tick checkbox để duplicate với tất cả cookies!")
        follow_layout.addWidget(self.single_input)
        
        # Input cho multiple IDs
        self.multiple_input = QTextEdit()
        self.multiple_input.setPlaceholderText("Nhập danh sách Follow IDs, mỗi ID một dòng:\n36709052\n12345678\n87654321\n\n💡 Tip: \n• Tick checkbox để duplicate ID đầu tiên với tất cả cookies\n• Hoặc nhập nhiều IDs để phân phối qua cookies")
        self.multiple_input.setMaximumHeight(140)
        self.multiple_input.hide()
        follow_layout.addWidget(self.multiple_input)

        # Checkbox để duplicate ID với tất cả cookies
        self.use_all_cookies = QCheckBox("🔄 Duplicate ID với tất cả cookies")
        self.use_all_cookies.setToolTip("Khi bật, sẽ sử dụng ID đã nhập để follow với tất cả 15 cookies")
        follow_layout.addWidget(self.use_all_cookies)

        main_layout.addWidget(follow_group)

        # Control buttons
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("🚀 Bắt đầu Follow")
        self.start_button.clicked.connect(self.start_follow)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        self.stop_button = QPushButton("⏹️ Dừng")
        self.stop_button.clicked.connect(self.stop_follow)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)

        # Progress section
        progress_group = QGroupBox("📊 Tiến trình")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("Sẵn sàng...")
        progress_layout.addWidget(self.progress_label)

        main_layout.addWidget(progress_group)

        # Log section
        log_group = QGroupBox("📝 Nhật ký hoạt động")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # Clear log button
        clear_log_button = QPushButton("🗑️ Xóa log")
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)

        main_layout.addWidget(log_group)

        # Results section
        results_group = QGroupBox("📈 Kết quả")
        results_layout = QVBoxLayout(results_group)

        self.results_label = QLabel("Chưa có kết quả")
        self.results_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        results_font = QFont()
        results_font.setPointSize(12)
        results_font.setBold(True)
        self.results_label.setFont(results_font)
        results_layout.addWidget(self.results_label)

        main_layout.addWidget(results_group)

    def on_mode_changed(self):
        """Xử lý khi thay đổi chế độ nhập"""
        if self.single_mode.isChecked():
            self.single_input.show()
            self.multiple_input.hide()
        else:
            self.single_input.hide()
            self.multiple_input.show()

    def import_cookie(self):
        """Import tất cả cookies từ file .txt"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Chọn file cookie",
            "",
            "Text files (*.txt);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    lines = file.readlines()

                # Lọc các dòng không trống
                self.cookies = [line.strip() for line in lines if line.strip()]

                if self.cookies:
                    self.cookie_status.setText(f"✓ Đã import {len(self.cookies)} cookie(s)")
                    self.cookie_status.setStyleSheet("color: green;")
                    self.log_message(f"Đã import {len(self.cookies)} cookie(s) thành công")

                    # Hiển thị preview của cookie đầu tiên
                    if len(self.cookies[0]) > 50:
                        preview = self.cookies[0][:50] + "..."
                    else:
                        preview = self.cookies[0]
                    self.log_message(f"Cookie đầu tiên: {preview}")
                else:
                    self.cookie_status.setText("✗ File cookie trống")
                    self.cookie_status.setStyleSheet("color: red;")
                    self.log_message("File cookie trống")

            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Không thể đọc file cookie:\n{str(e)}")
                self.log_message(f"Lỗi đọc file cookie: {str(e)}")

    def get_follow_ids(self):
        """Lấy danh sách follow IDs từ input"""
        follow_ids = []

        if self.single_mode.isChecked():
            # Chế độ single ID
            id_text = self.single_input.text().strip()
            if id_text:
                try:
                    follow_ids.append(str(int(id_text)))
                except ValueError:
                    QMessageBox.warning(self, "Cảnh báo", "Follow ID phải là số nguyên")
                    return []
        else:
            # Chế độ multiple IDs
            ids_text = self.multiple_input.toPlainText().strip()
            if ids_text:
                lines = ids_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line:
                        try:
                            follow_ids.append(str(int(line)))
                        except ValueError:
                            QMessageBox.warning(self, "Cảnh báo", f"ID không hợp lệ: {line}")
                            return []

        return follow_ids

    def duplicate_id_for_all_cookies(self, target_id):
        """Duplicate 1 ID cho tất cả cookies"""
        if not self.cookies or not target_id:
            return []

        # Tạo danh sách với cùng 1 ID cho tất cả cookies
        num_cookies = len(self.cookies)
        duplicated_ids = [str(target_id)] * num_cookies

        return duplicated_ids

    def start_follow(self):
        """Bắt đầu quá trình follow"""
        # Kiểm tra cookies
        if not self.cookies:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng import cookie trước")
            return

        # Lấy danh sách follow IDs
        if self.use_all_cookies.isChecked():
            # Duplicate ID với tất cả cookies
            user_ids = self.get_follow_ids()
            if not user_ids:
                QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập ít nhất một Follow ID để duplicate")
                return

            # Lấy ID đầu tiên để duplicate
            target_id = user_ids[0]
            follow_ids = self.duplicate_id_for_all_cookies(target_id)
            if not follow_ids:
                QMessageBox.warning(self, "Cảnh báo", "Không thể duplicate ID với cookies")
                return
            self.log_message(f"🔄 Chế độ duplicate: Sử dụng ID {target_id} với {len(follow_ids)} cookies")
        else:
            # Sử dụng IDs do người dùng nhập
            follow_ids = self.get_follow_ids()
            if not follow_ids:
                QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập ít nhất một Follow ID hợp lệ")
                return

        # Reset UI
        self.progress_bar.setValue(0)
        self.results_label.setText("Đang xử lý...")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Log bắt đầu
        self.log_message(f"Bắt đầu follow {len(follow_ids)} ID(s) với {len(self.cookies)} cookie(s)")

        # Tạo và chạy worker thread
        self.follow_worker = FollowWorker(self.cookies, follow_ids)
        self.follow_worker.progress_updated.connect(self.update_progress)
        self.follow_worker.status_updated.connect(self.log_message)
        self.follow_worker.finished.connect(self.on_follow_finished)
        self.follow_worker.start()

    def stop_follow(self):
        """Dừng quá trình follow"""
        if self.follow_worker and self.follow_worker.isRunning():
            self.follow_worker.terminate()
            self.follow_worker.wait()
            self.log_message("Đã dừng quá trình follow")
            self.reset_ui()

    def update_progress(self, value):
        """Cập nhật progress bar"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(f"Tiến trình: {value}%")

    def log_message(self, message):
        """Thêm message vào log"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        # Auto scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """Xóa log"""
        self.log_text.clear()

    def on_follow_finished(self, success_count, total_count):
        """Xử lý khi hoàn thành follow"""
        self.reset_ui()

        # Hiển thị kết quả
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        result_text = f"✅ Hoàn thành!\n{success_count}/{total_count} follow thành công ({success_rate:.1f}%)"
        self.results_label.setText(result_text)

        if success_rate == 100:
            self.results_label.setStyleSheet("color: green;")
        elif success_rate > 50:
            self.results_label.setStyleSheet("color: orange;")
        else:
            self.results_label.setStyleSheet("color: red;")

        # Log kết quả
        self.log_message(f"Hoàn thành! {success_count}/{total_count} follow thành công")

        # Hiển thị thông báo
        QMessageBox.information(
            self,
            "Hoàn thành",
            f"Đã hoàn thành quá trình follow!\n\n"
            f"Thành công: {success_count}/{total_count}\n"
            f"Tỷ lệ thành công: {success_rate:.1f}%"
        )

    def reset_ui(self):
        """Reset UI về trạng thái ban đầu"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Sẵn sàng...")

    @staticmethod
    def get_current_time():
        """Lấy thời gian hiện tại"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")


def main():
    """Hàm main để chạy ứng dụng"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Kling AI Follow Toolkit")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = KlingFollowGUI()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
