import requests
import json

followId = int(input("Enter followId: "))
cookie = input("Input cookie: ")
url = "https://api-app-global.klingai.com/api/user_follow/follow?caver=2"

payload = json.dumps({
  "dstId": followId,
  "followScene": "web_user_home",
  "followId": followId
})
headers = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'en',
  'cache-control': 'no-cache',
  'content-type': 'application/json',
  'origin': 'https://app.klingai.com',
  'pragma': 'no-cache',
  'priority': 'u=1, i',
  'referer': 'https://app.klingai.com/',
  'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'time-zone': 'Asia/Saigon',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
  'Cookie': f"{cookie}"
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
