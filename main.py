import requests
import json

url = "https://api-app-global.klingai.com/api/user_follow/follow?caver=2"

payload = json.dumps({
  "dstId": 35967449,
  "followScene": "web_user_home",
  "followId": "35967449"
})
headers = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'en',
  'cache-control': 'no-cache',
  'content-type': 'application/json',
  'origin': 'https://app.klingai.com',
  'pragma': 'no-cache',
  'priority': 'u=1, i',
  'referer': 'https://app.klingai.com/',
  'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'time-zone': 'Asia/Saigon',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
  'Cookie': 'weblogger_did=web_63C724F34E; __risk_web_device_id=338362609546607; KLING_LAST_ACCESS_REGION=global; _did=web_1E4BF5DC2290; did=web_986796052433670; _gcl_au=1.1.382562790.1753104506; _ga=GA1.1.4866573288.1753104506; _clck=1003049%7C2%7Cfwy%7C0%7C7517; userId=36912148; ksi18n.ai.portal_st=ChNrc2kxOG4uYWkucG9ydGFsLnN0EpABk0OlQMWh2YX9WY1SvBeckpzPIA1DBzbei1pJV25EqFlArnFLxMy7ZjIUFzm4iZ1WGaN1HHLLqOKbYYTWRKvHGS8JXpqnIeFXnrHa2kLTget8uoHttw8VvS94-SS33bdctRnNz-Hi2ZUyhW5Th2pXxvkjBnGjOqs09dxOJOkIDZsF1P5zKJLF3QG3bcrDDL0EGhLgeOSGnCOo2lrbIy6MggxI43ciIDZZAZiKi7FNu2KyHAiUM-si_FKMclZUvX0GVoFXPyIqKAUwAQ; ksi18n.ai.portal_ph=b7053b0b7db1ddd652badb9fefc8f5d0a3b8; passToken=ChNwYXNzcG9ydC5wYXNzLXRva2VuErABYBelcXeqB9XF6xhflbDK9_Mgnn7zffInXZNft2K9pYtfBk2JZvpeyBASds0QtrEuBlLezV4FRLORreKvDorzJsVnzyC29Nyi72gVy1ZZ3TKzF7TQJH_M6ZmpWLX8PO3pxgzr_Oniiuj69O9rJbZT3zxQUQIIySZdxUM0PSCZyG3aoc-W7YKynl_pRK92i1SlDrjpdLVs-OTQ5KD9S9hPZH1MZvcliP16L8Ys3RijfVkaEoYjqymCLVD7CXtMbMyBVMl2lyIg4ddiDfFLGI3hvH3GyX4CZ88eKy85BAqHLurGqDpPirgoBTAB; ak_bmsc=794FE4201BEAECA751A03D01B61B2BC5~000000000000000000000000000000~YAAQ5hUgF2dFHBSYAQAAINkrLRynfProa3t0/IH1TO7VScJyHjq9+dQzokRdnTjw+t08Zyz6eBsNlkVvEgs9xBd3zM59HENk3SIcj5bsboNmEOPj4vbrvtNbGOubTGUw1Hs8nCXIiGXSc2FsUhm9pAke0U7OXFvEryYLJzIpozVLp5/8nekmJ/kPVrpKlnGj8gReJGQ1EbX1TpK3vGTT7zn3vMnpUfXi4Fo6++gwH1/8LPuvmloJtexI7p0V18x2+2Q50Z4BUsVpII6WHiNGnVEHlTnw4cn91sB9sto0jOHHOc52MZXeK8bqp3XQoMhcukuzwtPpBuSFvWazvL87hwQKsrjz4bGaJ4I5//NkrnbN3IhXeABafe5WHgV5; bm_sv=E3C8851B8B5A018C677ED3B54AC6AE38~YAAQ5hUgFzZHHBSYAQAAV20sLRzt/HjV92NlW7xORjI2tsTo6ZWuJeLu6xIz7NDUY1ymDAQoTK58Aht2tNTS2FXMN04a5rRNPI29fMv3vDzEJFzk5ESLDudM5vR8JGjyPH6L5KtAsUXC79xjb2dguD0f2qxE93isnL9q4V3aAoG6FhIUbLWo1C7QNqjbwqBZfcUv2eOaA0V4twBCSWAmp4pz4NiTrBQj9UXjoRmwqy7EGg09Uop4C2bwr1pdmkphxA==~1; ak_bmsc=33C28D5AA425D29C9261DBEB00C3BDEB~000000000000000000000000000000~YAAQ6RUgFxdt9xOYAQAAlvgoLRyv84vldyil2LlL1v/6J9X2gqOP/YJZc41oF5OupCNMT7c2ZkGpsxULMwUXHg3+rz9Tglhwt15UDNO3yKa8GUIe6sA5UZCLU9C9hebgZEweF0Ed5S0vdHxWYWbI7RnqLJkGyhSsTxysIjMadHJapc0dsLZaI+WYGUEzH5A8OSUygFRVFIRBVNRdJfCvnqvwJmMBca0xhtyKUBqFC1AAaICQw7nwbz1cD1SpKhsdlSmWNEYFtWIqHEUoaCp7Csug0gP/HLN1aRg3QifwDbtaO4OMhsk1eiIDpFZEANhXATuBqx3Ah9VXyZywbYH8+QeaDLu0XH+wk/bhDw==; bm_sv=E3C8851B8B5A018C677ED3B54AC6AE38~YAAQ3YTered+TyaYAQAAaG4tLRzPZmJAoaYWIJLXyo62dOtRhystjHDyd3MUM3NiTTGte9mqqzeyTR5jTXOvqGC0flNq8nOjM+muZRvpWGQ3WK2vu0YBM324qIQbk+BuaFE9BYVuOgkAyuoRiizGLkEwqIKiFr15R+cJ0Eg/lSCwjmAj5xUN1GeqyF5xM5fDAS5ZKwZ7J+hG5JxHHtvuWDiR46FhKNxWthUMycN8wCaRNeYgSO79cXunI042VjHKdg==~1'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
