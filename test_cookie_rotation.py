#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng cookie rotation
"""

import requests
import json
import time

def test_cookie_rotation():
    """Test cookie rotation với nhiều ID"""
    
    # Đ<PERSON><PERSON> tất cả cookies từ file
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as file:
            cookies = [line.strip() for line in file.readlines() if line.strip()]
        print(f"✓ Đã đọc {len(cookies)} cookie(s) từ file")
    except Exception as e:
        print(f"✗ Lỗi đọc file cookie: {e}")
        return
    
    # Danh sách ID để test (có thể thêm ID khác)
    test_ids = [36709052, 12345678, 87654321, 11111111, 22222222]
    print(f"✓ Test với {len(test_ids)} ID(s): {test_ids}")
    
    # API endpoint
    url = "https://api-app-global.klingai.com/api/user_follow/follow?caver=2"
    
    # Base headers
    base_headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://app.klingai.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://app.klingai.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'time-zone': 'Asia/Saigon',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }
    
    success_count = 0
    
    print("\n" + "="*60)
    print("🔄 Bắt đầu test cookie rotation...")
    print("="*60)
    
    for i, follow_id in enumerate(test_ids):
        # Chọn cookie theo thứ tự rotation
        cookie_index = i % len(cookies)
        current_cookie = cookies[cookie_index]
        
        # Tạo headers với cookie hiện tại
        headers = base_headers.copy()
        headers['Cookie'] = current_cookie
        
        # Payload
        payload = json.dumps({
            "dstId": follow_id,
            "followScene": "web_user_home",
            "followId": follow_id
        })
        
        print(f"\n🔄 [{i+1}/{len(test_ids)}] Cookie {cookie_index+1}/{len(cookies)} - ID: {follow_id}")
        print(f"   Cookie preview: {current_cookie[:50]}...")
        
        try:
            response = requests.post(url, headers=headers, data=payload, timeout=10)
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get('result') == 1:
                    if response_data.get('status') == 200:
                        success_count += 1
                        user_name = response_data.get('data', {}).get('userName', 'Unknown')
                        print(f"   ✅ THÀNH CÔNG: Follow ID {follow_id} ({user_name})")
                    elif response_data.get('status') == 500 and 'Already followed' in str(response_data.get('error', {})):
                        success_count += 1
                        print(f"   ✅ ĐÃ FOLLOW: ID {follow_id} đã được follow trước đó")
                    else:
                        print(f"   ❌ THẤT BẠI: {response_data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ THẤT BẠI: {response_data.get('message', 'Unknown error')}")
            else:
                print(f"   ❌ HTTP ERROR: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {str(e)}")
        
        # Delay giữa các request
        if i < len(test_ids) - 1:
            print("   ⏳ Chờ 1 giây...")
            time.sleep(1)
    
    print("\n" + "="*60)
    print("📊 KẾT QUẢ CUỐI CÙNG:")
    print(f"   • Tổng số ID test: {len(test_ids)}")
    print(f"   • Số cookie sử dụng: {len(cookies)}")
    print(f"   • Thành công: {success_count}/{len(test_ids)}")
    print(f"   • Tỷ lệ thành công: {success_count/len(test_ids)*100:.1f}%")
    print("="*60)

if __name__ == "__main__":
    test_cookie_rotation()
