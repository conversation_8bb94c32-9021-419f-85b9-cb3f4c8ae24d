#!/usr/bin/env python3
"""
Script test để kiểm tra API follow với cookie và ID cụ thể
"""

import requests
import json

def test_follow_api():
    """Test API follow với cookie từ file và ID cụ thể"""
    
    # Đọc cookie từ file (sử dụng dòng đầu tiên)
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as file:
            cookie = file.readline().strip()
        print(f"✓ Đã đọc cookie từ file")
        print(f"Cookie preview: {cookie[:100]}...")
    except Exception as e:
        print(f"✗ Lỗi đọc file cookie: {e}")
        return
    
    # ID để test
    follow_id = 36709052
    print(f"✓ Test với Follow ID: {follow_id}")
    
    # API endpoint
    url = "https://api-app-global.klingai.com/api/user_follow/follow?caver=2"
    
    # Headers
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://app.klingai.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://app.klingai.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'time-zone': 'Asia/Saigon',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Cookie': cookie
    }
    
    # Payload
    payload = json.dumps({
        "dstId": follow_id,
        "followScene": "web_user_home",
        "followId": follow_id
    })
    
    print(f"✓ Gửi request đến API...")
    
    try:
        # Gửi request
        response = requests.post(url, headers=headers, data=payload, timeout=10)
        
        print(f"✓ Response status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✓ Response JSON:")
                print(json.dumps(response_data, indent=2, ensure_ascii=False))
                
                # Kiểm tra kết quả (API Kling sử dụng result=1 và status=200 cho thành công)
                if response_data.get('result') == 1 and response_data.get('status') == 200:
                    print(f"🎉 THÀNH CÔNG: Follow ID {follow_id} thành công!")
                    print(f"   - Follow relation: {response_data.get('data', {}).get('followRelation', 'Unknown')}")
                    print(f"   - User name: {response_data.get('data', {}).get('userName', 'Unknown')}")
                else:
                    print(f"❌ THẤT BẠI: {response_data.get('message', 'Unknown error')}")
                    
            except json.JSONDecodeError:
                print(f"✗ Không thể parse JSON response")
                print(f"Raw response: {response.text}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timeout")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 Test Kling AI Follow API")
    print("=" * 50)
    test_follow_api()
    print("=" * 50)
    print("✅ Test hoàn thành!")
